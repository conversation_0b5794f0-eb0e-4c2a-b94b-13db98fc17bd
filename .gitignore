# bin directory bin
/bin/
/install/bin/

# configuration files
configFiles/
# json files
*.json
# 

# .out log file(generated by nohup defualt)
*.out
# .bak backups file
*.bak

# swap files (usually generated by vim)
*.swap

# cppcheck scrips and reports
*cppcheck*
*cppchk*

# Prerequisites
*.d

# Compiled Object files
*.slo
*.lo
*.o
*.obj

# Protobuf generated files
*.pb.h
*.pb.cc

# Precompiled Headers
*.gch
*.pch

# Compiled Dynamic libraries
# *.so
*.dylib
*.dll

# Compiled Static libraries
*.lai
*.la
# *.a
*.lib

# Executables for windows
*.exe

# usr configurations
*.usr
*.user
*.pro.user.*
*.orig
*.autosave

# build directory
build*/
build/*

# libraries
deploy/

# protobuf source files
*.pb.h
*.pb.cc

# Visual Studio
.vs/
*.suo
*.user
*.userosscache
*.userprefs
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
// ethercat/EtherCATInterface.cpp

#include <iostream>
#include <unistd.h>        // sleep
#include <string.h>        // memset
#include <etherlab/ecrt.h> // IGH EtherCAT 主站API
#include <signal.h>
#include <math.h>
#include "ethercat/EtherCATInterface.h"

#include "Utilities/SharedMemoryManager.h"
#include "Parameters/SharedDataType.h"
#include "Parameters/GlobalParameters.h"
#include <chrono>


#ifdef USE_DYNAMICS_MODEL
#include "dynamics/dynamical_model.h"
DynamicalModel model;
#endif

#define SYNC_MASTER_TO_REF

#define CYCLE_TIME_NS (1000000) // 1ms
#define NSEC_PER_SEC (1000000000L)

#define VENDOR_ID (0x000116C7)
#define PRODUCT_ID (0x006b0402)
#define CLOCK_TO_USE CLOCK_REALTIME

#define DIFF_NS(A, B) (((B).tv_sec - (A).tv_sec) * NSEC_PER_SEC + \(B).tv_nsec - (A).tv_nsec)

#define TIMESPEC2NS(T) ((uint64_t)(T).tv_sec * NSEC_PER_SEC + (T).tv_nsec)

constexpr int NUM_SLAVES = 6;

uint32_t Joint_Zero_Offset[6] = {500000, 150000}; //记录偏移

uint32_t SlaveVID[] = {0x000116C7, 0x000116C7, 0x000116C7, 0x000116C7, 0x000116C7, 0x000116C7};
uint32_t SlavePID[] = {0x003e0402, 0x003e0402, 0x003e0402, 0x003e0402, 0x003e0402, 0x003e0402};
// uint32_t SlavePID[] = {0x005e0402, 0x006b0402, 0x005e0402, 0x005e0402, 0x005e0402, 0x005e0402};
// #define PRODUCT_ID 0x005e0402, 0x006b0402, 0x006b0402;


/*Offsets for PDO entries*/
static struct{
    unsigned int Status_Word[NUM_SLAVES];
    unsigned int Control_word[NUM_SLAVES];
    unsigned int Target_Position[NUM_SLAVES];
    unsigned int Position_Actual_Value[NUM_SLAVES];
    unsigned int Control_Mode_Display[NUM_SLAVES];
    unsigned int Control_Mode[NUM_SLAVES];
    unsigned int Target_velocity[NUM_SLAVES];
    unsigned int Velocity_actual_value[NUM_SLAVES];

    unsigned int targetTorque[NUM_SLAVES];
    unsigned int actualTorque[NUM_SLAVES];

    unsigned int Max_motor_speed[NUM_SLAVES];
    unsigned int Profile_acceleration[NUM_SLAVES];
    unsigned int Profile_dec[NUM_SLAVES];
    unsigned int Profile_Max_Velocity[NUM_SLAVES];
    unsigned int Max_Torque[NUM_SLAVES];
    unsigned int Min_Torque[NUM_SLAVES];
    unsigned int Profile_Velocity[NUM_SLAVES];
    unsigned int Max_acceleration[NUM_SLAVES];
    unsigned int Max_dec[NUM_SLAVES];
}offset;


// 列出所有需要通信的对象字典条目
ec_pdo_entry_info_t slave_pdo_entries[] = {
    {0x6040, 0x00, 16}, // Control word
//    {0x6060, 0x00,  8}, // Control mode
    {0x607A, 0x00, 32}, // Target position
    {0x60FF, 0x00, 32}, // Target Velocity
    {0x6071, 0x00, 16}, // Target torque

    //
    {0x6041, 0x00, 16}, // Status word
//    {0x6061, 0x00,  8}, // Control_Mode_Display
    {0x6064, 0x00, 32}, // Actual position
    {0x606C, 0x00, 32}, // Actual velocity
    {0x6077, 0x00, 16}, // Actual torque
};

// 按方向分组，组织成PDO包
// 0x1600：输出PDO
// 0x1A00：输入PDO
ec_pdo_info_t slave_pdos[] = {
    {0x1600, 4, slave_pdo_entries + 0},
    {0x1a00, 4, slave_pdo_entries + 4},
};


// 把PDO挂到正确的同步管理器上
ec_sync_info_t slave_syncs[] = {
        {0, EC_DIR_OUTPUT, 0, NULL, EC_WD_DISABLE},
        {1, EC_DIR_INPUT, 0, NULL, EC_WD_DISABLE},
        {2, EC_DIR_OUTPUT, 1, slave_pdos + 0, EC_WD_ENABLE},
        {3, EC_DIR_INPUT, 1, slave_pdos + 1, EC_WD_DISABLE},
        {0xff}
    };
    

    // SharedMemoryManager<SharedMemoryData>  shm = SharedMemoryManager<SharedMemoryData>(SharedMemoryManager<SharedMemoryData>::Attacher, true);
extern SharedMemoryManager<SharedMemoryData> shm;

EtherCATInterface::EtherCATInterface()
    : running_(false), master_(nullptr), domain_(nullptr), domain_pd_(nullptr)
{

}

EtherCATInterface::~EtherCATInterface()
{

}

// 信号处理函数
void EtherCATInterface::signal_handler() {
        //deal ethercat signal
        ecrt_master_receive(master_);
        ecrt_domain_process(domain_);
        check_domain_state();
        //Check for master state
        check_master_state();
        //Check for slave configuration state(s)
        for(int i = 0; i < NUM_SLAVES; i++)
        {
            check_slave_config_states(slave_config[i], i);
        }

        for(int i = 0; i < NUM_SLAVES; i++) {
            //stop motor
            EC_WRITE_S32(domain_pd_ + offset.Target_velocity[i], 0);
            //disable motor
            EC_WRITE_U16(domain_pd_ + offset.Control_word[i], 0);
            //change not mode
//            EC_WRITE_S8(domain_pd_ + offset.Control_Mode[i], 0);
        }

        //stop deal
        ecrt_domain_queue(domain_);
        ecrt_master_send(master_);

        //exit handle
        munlockall();
        exit(0);
}
/*****************************************************************************/
/* Note: Anything relying on definition of SYNC_MASTER_TO_REF is essentially copy-pasted from /rtdm_rtai_dc/main.c */

#ifdef SYNC_MASTER_TO_REF

/* First used in system_time_ns() */
static int64_t  system_time_base = 0LL;
/* First used in sync_distributed_clocks() */
static uint64_t dc_time_ns = 0;
static int32_t  prev_dc_diff_ns = 0;
/* First used in update_master_clock() */
static int32_t  dc_diff_ns = 0;
static unsigned int cycle_ns = CYCLE_TIME_NS;
static uint8_t  dc_started = 0;
static int64_t  dc_diff_total_ns = 0LL;
static int64_t  dc_delta_total_ns = 0LL;
static int      dc_filter_idx = 0;
static int64_t  dc_adjust_ns;
#define DC_FILTER_CNT          1024
/** Return the sign of a number
 *
 * ie -1 for -ve value, 0 for 0, +1 for +ve value
 *
 * \retval the sign of the value
 */
#define sign(val) \
    ({ typeof (val) _val = (val); \
    ((_val > 0) - (_val < 0)); })

static uint64_t dc_start_time_ns = 0LL;

#endif
/*****************************************************************************/

#ifdef SYNC_MASTER_TO_REF

/** Get the time in ns for the current cpu, adjusted by system_time_base.
 *
 * \attention Rather than calling rt_get_time_ns() directly, all application
 * time calls should use this method instead.
 *
 * \ret The time in ns.
 */
uint64_t system_time_ns(void)
{
    struct timespec time;
    int64_t time_ns;
    clock_gettime(CLOCK_MONOTONIC, &time);
    time_ns = TIMESPEC2NS(time);

    if (system_time_base > time_ns)
    {
        printf("%s() error: system_time_base greater than"
               " system time (system_time_base: %ld, time: %lu\n",
            __func__, system_time_base, time_ns);
        return time_ns;
    }
    else
    {
        return time_ns - system_time_base;
    }
}
/** Synchronise the distributed clocks
 */
void EtherCATInterface::sync_distributed_clocks(void)
{
    uint32_t ref_time = 0;
    uint64_t prev_app_time = dc_time_ns;

    dc_time_ns = system_time_ns();

    // set master time in nano-seconds
    ecrt_master_application_time(master_, dc_time_ns);

    // get reference clock time to synchronize master cycle
    ecrt_master_reference_clock_time(master_, &ref_time);
    dc_diff_ns = (uint32_t) prev_app_time - ref_time;

    // call to sync slaves to ref slave
    ecrt_master_sync_slave_clocks(master_);

    // Additional synchronization check
    static int sync_check_counter = 0;
    sync_check_counter++;

    if (sync_check_counter % 1000 == 0) // Check every second
    {
        // Check if DC synchronization is working properly
        if (abs(dc_diff_ns) > CYCLE_TIME_NS / 4) // If drift is more than 25% of cycle time
        {
            printf("Warning: Large DC drift detected: %d ns\n", dc_diff_ns);
        }
    }
}
/** Update the master time based on ref slaves time diff
 *
 * called after the ethercat frame is sent to avoid time jitter in
 * sync_distributed_clocks()
 */
void EtherCATInterface::update_master_clock(void)
{

    // calc drift (via un-normalised time diff)
    int32_t delta = dc_diff_ns - prev_dc_diff_ns;
    //printf("%d\n", (int) delta);
    prev_dc_diff_ns = dc_diff_ns;

    // normalise the time diff
    dc_diff_ns = ((dc_diff_ns + (cycle_ns / 2)) % cycle_ns) - (cycle_ns / 2);

    // only update if primary master
    if (dc_started)
    {

        // add to totals
        dc_diff_total_ns += dc_diff_ns;
        dc_delta_total_ns += delta;
        dc_filter_idx++;

        if (dc_filter_idx >= DC_FILTER_CNT)
        {
            // add rounded delta average
            dc_adjust_ns += ((dc_delta_total_ns + (DC_FILTER_CNT / 2)) / DC_FILTER_CNT);

            // and add adjustment for general diff (to pull in drift)
            dc_adjust_ns += sign(dc_diff_total_ns / DC_FILTER_CNT);

            // limit crazy numbers (0.1% of std cycle time)
//            if (dc_adjust_ns < -1000)
//            {
//                dc_adjust_ns = -1000;
//            }
//            if (dc_adjust_ns > 1000)
//            {
//                dc_adjust_ns =  1000;
//            }
            if (dc_adjust_ns < - (CYCLE_TIME_NS / 1000))
            {
                dc_adjust_ns = -(CYCLE_TIME_NS / 1000);
            }
            if (dc_adjust_ns > (CYCLE_TIME_NS / 1000))
            {
                dc_adjust_ns =  (CYCLE_TIME_NS / 1000);
            }

            // reset
            dc_diff_total_ns = 0LL;
            dc_delta_total_ns = 0LL;
            dc_filter_idx = 0;
        }

        // add cycles adjustment to time base (including a spot adjustment)
        system_time_base += dc_adjust_ns + sign(dc_diff_ns);
    }
    else
    {
        dc_started = (dc_diff_ns != 0);

        if (dc_started)
        {
            // output first diff
            printf("DC synchronization started. First master diff: %d ns.\n", dc_diff_ns);
            printf("DC filter will stabilize after %d cycles.\n", DC_FILTER_CNT);

            // record the time of this initial cycle
            dc_start_time_ns = dc_time_ns;
        }
    }
}

#endif

void ODwrite(ec_master_t* master, uint16_t slavePos, uint16_t index, uint8_t subIndex, uint8_t objectValue)
{
    /* Blocks until a reponse is received */
    uint8_t retVal = ecrt_master_sdo_download(master, slavePos, index, subIndex, &objectValue, sizeof(objectValue), NULL);
    /* retVal != 0: Failure */
    if (retVal)
        printf("OD write unsuccessful for slave %d, index 0x%04X, subindex 0x%02X, error: %d\n",
               slavePos, index, subIndex, retVal);
    else
        printf("OD write successful for slave %d, index 0x%04X, subindex 0x%02X\n",
               slavePos, index, subIndex);
}

void ODwrite32(ec_master_t* master, uint16_t slavePos, uint16_t index, uint8_t subIndex, uint32_t objectValue)
{
    uint8_t retVal = ecrt_master_sdo_download(master, slavePos, index, subIndex, (uint8_t*)&objectValue, sizeof(objectValue), NULL);
    if (retVal)
        printf("OD write32 unsuccessful for slave %d, index 0x%04X, subindex 0x%02X, error: %d\n",
               slavePos, index, subIndex, retVal);
    else
        printf("OD write32 successful for slave %d, index 0x%04X, subindex 0x%02X\n",
               slavePos, index, subIndex);
}

bool initDrive(ec_master_t* master, uint16_t slavePos, uint8_t mode)
{
    printf("Initializing drive %d with mode 0x%02X\n", slavePos, mode);

    /* Reset alarm first */
    ODwrite(master, slavePos, 0x6040, 0x00, 0x80);
    usleep(10000); // 10ms delay

    /* Mode of operation*/
    ODwrite(master, slavePos, 0x6060, 0x00, mode);  // 0x08 for CSP mode
    usleep(10000); // 10ms delay

    /* Set some basic parameters for better synchronization */
    // Profile velocity (for smooth transitions)
    ODwrite32(master, slavePos, 0x6081, 0x00, 1000000); // 1000000 inc/s

    // Profile acceleration
    ODwrite32(master, slavePos, 0x6083, 0x00, 500000);  // 500000 inc/s²

    // Profile deceleration
    ODwrite32(master, slavePos, 0x6084, 0x00, 500000);  // 500000 inc/s²

    printf("Drive %d initialization completed\n", slavePos);
    return true;
}

bool EtherCATInterface::init()
{
    printf("Starting EtherCAT master initialization...\n");

    master_ = ecrt_request_master(0);
    if (!master_)
    {
        std::cerr << "Failed to request master.\n";
        return false;
    }
    printf("EtherCAT master requested successfully.\n");

    domain_ = ecrt_master_create_domain(master_);
    if (!domain_)
    {
        std::cerr << "Failed to create domain.\n";
        return false;
    }
    printf("EtherCAT domain created successfully.\n");

    // Note: SDO operations moved to after master activation

    // 初始化 slaves
    printf("Configuring %d slaves...\n", NUM_SLAVES);
    for (int i = 0; i < NUM_SLAVES; i++)
    {
        printf("Configuring slave %d (VID: 0x%08X, PID: 0x%08X)...\n", i, SlaveVID[i], SlavePID[i]);

        slave_config[i] = ecrt_master_slave_config(master_, 0, i, SlaveVID[i], SlavePID[i]);
        if (!slave_config[i])
        {
            std::cerr << "Failed to get slave config for slave " << i << ".\n";
            return false;
        }
        printf("Slave %d config obtained successfully.\n", i);

        if (ecrt_slave_config_pdos(slave_config[i], EC_END, slave_syncs))
        {
            std::cerr << "Failed to configure PDOs for slave " << i << ".\n";
            return false;
        }
        printf("Slave %d PDOs configured successfully.\n", i);
    }
    printf("All slaves configured successfully.\n");

    printf("Configuring PDOs...\n");

    std::vector<ec_pdo_entry_reg_t> domain_regs;
//    domain_regs.reserve(NUM_SLAVES * 8 + 1);

    for (int i = 0; i < NUM_SLAVES; i++) {
        uint16_t alias = 0;
        uint16_t position = static_cast<uint16_t>(i);

        // {Alias, Position, Vendor ID, Product Code, PDO Index, PDO entry subindex, Offset, bit_position}
        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x6040, 0x00, &offset.Control_word[i], nullptr});
//        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x6060, 0x00, &offset.Control_Mode[i], nullptr});
        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x607A, 0x00, &offset.Target_Position[i], nullptr});
        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x60FF, 0x00, &offset.Target_velocity[i], nullptr});
        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x6071, 0x00, &offset.targetTorque[i], nullptr});

        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x6041, 0x00, &offset.Status_Word[i], nullptr});
        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x6064, 0x00, &offset.Position_Actual_Value[i], nullptr});
        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x606c, 0x00, &offset.Velocity_actual_value[i], nullptr});
        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x6077, 0x00, &offset.actualTorque[i], nullptr});
//        domain_regs.push_back({alias, position, SlaveVID[i], SlavePID[i], 0x6061, 0x00, &offset.Control_Mode_Display[i], nullptr});
    }

    domain_regs.push_back({});




    if (ecrt_domain_reg_pdo_entry_list(domain_, domain_regs.data()))
    {
        fprintf(stderr, "pdo入口注册失败\n");
        return -1;
    }

    // Configure distributed clocks for better synchronization
    printf("Configuring distributed clocks...\n");
    for(int i = 0; i < NUM_SLAVES; i++)
    {
        // Use more conservative DC settings for better stability
        // AssignActivate: 0x0300 (SYNC0 enabled)
        // SYNC0 cycle: same as master cycle
        // SYNC0 shift: half cycle for better timing
        ecrt_slave_config_dc(slave_config[i], 0x0300, CYCLE_TIME_NS, CYCLE_TIME_NS/2, 0, 0);
        printf("DC configured for slave %d\n", i);
    }

#ifdef SYNC_MASTER_TO_REF
    /* Initialize master application time. */
    printf("Initializing distributed clock synchronization...\n");
    dc_start_time_ns = system_time_ns();
    dc_time_ns = dc_start_time_ns;
    ecrt_master_application_time(master_, dc_start_time_ns);

    if (ecrt_master_select_reference_clock(master_, slave_config[0]))
    {
        printf("Selecting slave 0 as reference clock failed!\n");
        return false;
    }
    printf("Slave 0 selected as reference clock successfully.\n");
#endif

    printf("Activating EtherCAT master...\n");
    if (ecrt_master_activate(master_))
    {
        std::cerr << "Failed to activate master.\n";
        return false;
    }
    printf("EtherCAT master activated successfully.\n");

    domain_pd_ = ecrt_domain_data(domain_);
    if (!domain_pd_)
    {
        std::cerr << "Failed to get domain data pointer.\n";
        return false;
    }
    printf("Domain data pointer obtained successfully.\n");

    // Wait for slaves to reach operational state
    printf("Waiting for slaves to reach operational state...\n");
    if (!waitForSlavesOperational())
    {
        std::cerr << "Failed to bring all slaves to operational state.\n";
        return false;
    }

    // Now perform SDO configuration after master is active
    printf("Performing post-activation slave configuration...\n");
    for (int i = 0; i < NUM_SLAVES; i++)
    {
        if (!initDrive(master_, i, 0x08)) // CSP mode
        {
            std::cerr << "Failed to initialize drive " << i << ".\n";
            return false;
        }
    }
    printf("All drives initialized successfully.\n");

    // Final synchronization verification
    printf("Performing final synchronization verification...\n");
    if (!verifySynchronization())
    {
        printf("WARNING: Initial synchronization verification failed!\n");
        printSynchronizationStatus();
    }
    else
    {
        printf("Initial synchronization verification passed.\n");
    }

    // Wait for all motors to reach operation_enable state
    printf("Waiting for all motors to reach operation_enable state...\n");
    if (!waitForAllSlavesInState(operation_enable, 10000)) // 10 second timeout
    {
        printf("WARNING: Not all motors reached operation_enable state\n");
        printSynchronizationStatus();
    }
    else
    {
        printf("All motors are in operation_enable state.\n");
    }

    printf("EtherCAT initialization completed successfully.\n");
    return true;
}

void EtherCATInterface::runTask()
{
    static uint16_t cycle_counter = 0;
#ifdef USE_DYNAMICS_MODEL
        // if (cycle_counter %100 == 0)
        // {
    //        auto start = std::chrono::high_resolution_clock::now();

    //        // 期望轨迹
    //        Vector6d qd;
    ////        q.setZero();
    ////        q << 30, 45, 60;
    //        qd << 30, 45, 60, 30, 60, 45;
    //        qd *= M_PI/180;

    //        Vector6d dqd = qd*2;
    //        Vector6d ddqd = qd*3;

    //        // 实际轨迹
    //        Vector6d q = qd;
    //        Vector6d dq = dqd;
    //        Vector6d ddq = ddqd;


    //        Vector6d tau = model.rnea(q, dq, ddq);

    //        ddq.setZero();
    //        dq = dqd;
    //        ddq.setZero();
    //        Vector6d zero6d = Vector6d::Zero();
    //        Vector6d tau_coriolis = model.rnea(q, dq, zero6d);

    //        Vector6d G;
    //        dq.setZero();
    //        ddq.setZero();
    //        G = model.rnea(q, dq, ddq);

    //        Eigen::MatrixXd M;
    //        M.resize(6,6);
    //        M.setZero();

    //        Vector6d tau_i;
    //        tau_i.setZero();
    //        dq.setZero();
    //        for(short int i=0; i<6; i++){
    //            ddq.setZero();
    //            ddq[i] = 1;
    //            tau_i = model.rnea( q, dq, ddq);
    //            for(short int j=0; j<6; j++){
    //                M(j,i) = tau_i[j] - G[j];
    //            }
    //            tau_i.setZero();
    //        }
    //        std::cout << "--------------------tau vector------------------" << std::endl;
    //        std::cout << tau << std::endl;

    //        std::cout << "--------------------M matrix--------------------" << std::endl;
    //        std::cout << M << std::endl;

    //        std::cout << "--------------------C(q,dq)q+G(q)--------------------" << std::endl;
    //        std::cout << tau_coriolis << std::endl;

    //        std::cout << "--------------------G(q)------------------------" << std::endl;
    //        std::cout << G << std::endl;


    //        auto end = std::chrono::high_resolution_clock::now();
    //        std::chrono::duration<double, std::milli> duration = end - start;
    //        std::cout << "Takes Time: " << duration.count() << " ms" << std::endl;
        // }
#endif

    // 更新和发送过程数据
    ecrt_master_receive(master_);
    ecrt_domain_process(domain_);
    motor_start_flag = 1;

    cycle_counter++;
    if(!(cycle_counter % 500))
    {
        cycle_counter = 0;
        check_domain_state();
        check_master_state();
        for(int i = 0; i < NUM_SLAVES; i++)
        {
            check_slave_config_states(slave_config[i], i);
        }

        // Perform synchronization verification every 500ms
        if (!verifySynchronization())
        {
            printf("WARNING: Synchronization issues detected!\n");
        }
    }

    // Quick synchronization check every 100 cycles (100ms)
    if(!(cycle_counter % 100))
    {
        performSynchronizationCheck();
    }

    

    // read status word
    uint16_t state_value[NUM_SLAVES];
    for (int i = 0; i < NUM_SLAVES; i++)
    {
        state_value[i] = EC_READ_U16(domain_pd_ + offset.Status_Word[i]); // 读取电机状态字
    }

    //printf("statue: %d\n", state_value[1]);

    cia402_state_t servo_state[NUM_SLAVES];

    bool all_switched_on = true;
    bool all_operation_enable = true;



    // 电机当前位置的脉冲值
    std::array<int32_t, NUM_SLAVES> actual_pos_pulse;
    std::array<int32_t, NUM_SLAVES> actual_vel;
    std::array<int32_t, NUM_SLAVES> actual_torque;
    for (int16_t i = 0; i < NUM_SLAVES; i++)
    {
        servo_state[i] = get_axis_state(state_value[i]);
        all_switched_on = all_switched_on && (servo_state[i] == switched_on);
        all_operation_enable = all_operation_enable && (servo_state[i] == operation_enable);
        
        actual_pos_pulse[i] = EC_READ_S32(domain_pd_ + offset.Position_Actual_Value[i]);

        actual_vel[i] = EC_READ_S32(domain_pd_ + offset.Velocity_actual_value[i]);

        actual_torque[i] = EC_READ_S32(domain_pd_ + offset.actualTorque[i]);
    }
    static std::array<signed int, NUM_SLAVES> target_pos_pulse;

    static uint32_t print_cnt = 0;
    if (print_cnt == 100)
    {
        // printf("Joint Current: %d %d\n", actual_pos_pulse[0], actual_pos_pulse[1]);   
        // printf("target pulse: %d %d\n",target_pos_pulse[0], target_pos_pulse[1]);
        // printf("Joint Velocity: %d %d\n", actual_vel[0], actual_vel[1]); 
        // printf("GlobalParams::isMoving: %d\n", GlobalParams::isMoving);
  
        // printf("Joint Torque: %d %d\n", actual_torque[0], actual_torque[1]); 

        print_cnt = 0;
    }   
    print_cnt++;

    // static std::array<signed int, NUM_SLAVES> target_pos_pulse;
    // target_pos_pulse = actual_pos_pulse;

    if (all_operation_enable)
    {

        // 获取电机指令
        LowLevelCommand montor_cmd;
        if (GlobalParams::joint_commands.try_dequeue(montor_cmd))
        {
            for(int i = 0; i < NUM_SLAVES; i++) 
            {

                target_pos_pulse[i] = montor_cmd.joint_pos[i];
            }
            // printf("Joint Current: %d %d\n", actual_pos_pulse[0], actual_pos_pulse[1]);   
            // printf("Joint Velocity: %d %d\n", actual_vel[0], actual_vel[1]);   
        }
        
        for (int i = 0; i < NUM_SLAVES; i++)
        {
//            static int value[NUM_SLAVES] = {0};
//            auto cur_value = (EC_READ_S32(domain_pd_ + offset.Position_Actual_Value[i]));
//            EC_WRITE_U32(domain_pd_ + offset.Target_Position[i], cur_value + value[i]);
//            value[i] += 0xff * print_cnt;

            // auto value = (EC_READ_S32(domain_pd_ + offset.Position_Actual_Value[i]));
            EC_WRITE_U32(domain_pd_ + offset.Target_Position[i], target_pos_pulse[i]);
        }
    }
    
    // Enhanced CiA402 state machine with better error handling
    if (motor_start_flag == 1)
    {
        static int state_transition_counter[NUM_SLAVES] = {0};
        static cia402_state_t previous_state[NUM_SLAVES] = {no_ready_to_switch_on};

        for (int16_t i = 0; i < NUM_SLAVES; i++)
        {
            // Check for state changes
            if (servo_state[i] != previous_state[i])
            {
                printf("Slave %d state transition: %d -> %d\n",
                       i, previous_state[i], servo_state[i]);
                previous_state[i] = servo_state[i];
                state_transition_counter[i] = 0;
            }
            else
            {
                state_transition_counter[i]++;
            }

            // Timeout detection (stuck in state for too long)
            if (state_transition_counter[i] > 1000) // 1 second at 1kHz
            {
                printf("WARNING: Slave %d stuck in state %d for >1s\n",
                       i, servo_state[i]);
                state_transition_counter[i] = 0;

                // Force fault reset if stuck
                if (servo_state[i] == fault)
                {
                    printf("Forcing fault reset for slave %d\n", i);
                    EC_WRITE_U16(domain_pd_ + offset.Control_word[i], 0x80);
                    continue;
                }
            }

            switch (servo_state[i])
            {
            case (no_ready_to_switch_on):
                // Wait for drive to initialize
                printf("Slave %d: Waiting for drive initialization...\n", i);
                break;

            case (switch_on_disable):
                // Shutdown command to move to ready_to_switch_on
                EC_WRITE_U16(domain_pd_ + offset.Control_word[i], CiA402::CONTROLWORD_SHUTDOWN);
                break;

            case (ready_to_switch_on):
                // Switch on command to move to switched_on
                EC_WRITE_U16(domain_pd_ + offset.Control_word[i], CiA402::CONTROLWORD_SWITCH_ON);
                target_pos_pulse[i] = actual_pos_pulse[i]; // Set current position as target
                break;

            case (switched_on):
                // Enable operation to move to operation_enable
                EC_WRITE_U16(domain_pd_ + offset.Control_word[i], CiA402::CONTROLWORD_ENABLE_OPERATION);
                break;

            case (operation_enable):
                // Motor is ready for operation - maintain enable operation
                EC_WRITE_U16(domain_pd_ + offset.Control_word[i], CiA402::CONTROLWORD_ENABLE_OPERATION);
                break;

            case (quick_stop_active):
                printf("Slave %d in quick stop - attempting to recover\n", i);
                // Disable voltage then restart sequence
                EC_WRITE_U16(domain_pd_ + offset.Control_word[i], CiA402::CONTROLWORD_DISABLE_VOLTAGE);
                break;

            case (fault_reaction_active):
                printf("Slave %d in fault reaction - waiting for fault state\n", i);
                // Wait for transition to fault state
                break;

            case (fault):
                printf("Slave %d in fault state - resetting\n", i);
                // Fault reset
                EC_WRITE_U16(domain_pd_ + offset.Control_word[i], CiA402::CONTROLWORD_FAULT_RESET);
                break;

            default:
                printf("Slave %d in unknown state %d\n", i, servo_state[i]);
                // Try fault reset as recovery
                EC_WRITE_U16(domain_pd_ + offset.Control_word[i], CiA402::CONTROLWORD_FAULT_RESET);
                break;
            }
        }
    }
    else if (motor_start_flag == 0)
    {
        // disable motor
        for (int16_t i = 0; i < NUM_SLAVES; i++)
        {
            EC_WRITE_U16(domain_pd_ + offset.Control_word[i], 0x00);
        }
    }

//    // sync every cycle
//    clock_gettime(CLOCK_TO_USE, &time);
//    ecrt_master_sync_reference_clock_to(master_, TIMESPEC2NS(time));
//    ecrt_master_sync_slave_clocks(master_);

//    sendProcessData();

#ifdef SYNC_MASTER_TO_REF
    ecrt_domain_queue(domain_);
    // sync distributed clock just before master_send to set
    // most accurate master clock time
    sync_distributed_clocks();

    /* Sends all datagrams in the queue.
   This method takes all datagrams that have been queued for transmission,
   puts them into frames, and passes them to the Ethernet device for sending.
*/
    ecrt_master_send(master_);
    // update the master clock
    // Note: called after ecrt_master_send() to reduce time
    // jitter in the sync_distributed_clocks() call
    update_master_clock();
#endif


    // uint32_t jointsStateFlag = 0;

    // for(int i = 0; i < NUM_SLAVES; i++) 
    // {
    //     // 当实际脉冲和目标脉冲在正负2个脉冲值误差范围内时，认为已到达目标位置

    //     if (abs(pos[i] - actual_pos_pulse[i]) <= 2)
    //         jointsStateFlag |= (1 << i);
    //     else
    //         jointsStateFlag &= ~(1 << i);
    // }

    // 传递电机状态 以供其他模块使用
    RobotState cur_state;
    bool moveFlag = false;
    for (int i = 0; i < NUM_SLAVES; i++)
    {

        // 脉冲 -> 角度(弧度)
        double current_pos; 
        // current_pos = actual_pos_pulse[i] * robot.m_gearRatio[i] * (M_PI / 360.0f);
        current_pos = actual_pos_pulse[i] / 250000.0 * (M_PI / 13.1072);
        cur_state.joint_state[i].position = current_pos;
        cur_state.joint_state[i].velocity = actual_vel[i] / 250000.0 * (M_PI / 13.1072);
        // cur_state.joint_state[i].torque = actual_torque[i]; 

        if (abs(target_pos_pulse[i] - actual_pos_pulse[i]) <= 5)
        {
            cur_state.joint_state[i].motor_state = 0;
        }
        else 
        {
            cur_state.joint_state[i].motor_state = 1;
        }
        moveFlag = moveFlag || cur_state.joint_state[i].motor_state;
        GlobalParams::isMoving = moveFlag;
        
    }

    shm().state_buffer.write(cur_state);

    //    robot->updateJointStates();

    //clock_nanosleep(CLOCK_MONOTONIC, TIMER_ABSTIME, &wakeup_time, NULL);
}




void EtherCATInterface::check_domain_state()
{
    ec_domain_state_t ds;
    ecrt_domain_state(domain_ , &ds);
    if(ds.working_counter != domain_state.working_counter)
    {
        printf("Domain working counter changed: %u -> %u",
               domain_state.working_counter, ds.working_counter);
        if (ds.working_counter < NUM_SLAVES * 2) // Expected: 2 PDOs per slave
        {
            printf(" (WARNING: Incomplete frame transmission detected!)\n");
        }
        else
        {
            printf(" (OK)\n");
        }
    }
    if(ds.wc_state != domain_state.wc_state)
    {
        printf("Domain working counter state changed: %u -> %u\n",
               domain_state.wc_state, ds.wc_state);
        switch(ds.wc_state)
        {
            case EC_WC_ZERO:
                printf("  -> No slaves responded\n");
                break;
            case EC_WC_INCOMPLETE:
                printf("  -> Some slaves did not respond\n");
                break;
            case EC_WC_COMPLETE:
                printf("  -> All slaves responded correctly\n");
                break;
            default:
                printf("  -> Unknown working counter state\n");
                break;
        }
    }
    domain_state = ds;
}

void EtherCATInterface::check_master_state()
{
    ec_master_state_t ms;
    ecrt_master_state(master_, &ms);
    if (ms.slaves_responding != master_state.slaves_responding)
    {
        printf("Slaves responding: %u -> %u",
               master_state.slaves_responding, ms.slaves_responding);
        if (ms.slaves_responding < NUM_SLAVES)
        {
            printf(" (WARNING: %d slaves not responding!)\n",
                   NUM_SLAVES - ms.slaves_responding);
        }
        else
        {
            printf(" (All slaves responding)\n");
        }
    }
    if (ms.al_states != master_state.al_states)
    {
        printf("Master AL states changed: 0x%02X -> 0x%02X\n",
               master_state.al_states, ms.al_states);
        // Decode AL states
        if (ms.al_states & 0x01) printf("  - At least one slave in INIT\n");
        if (ms.al_states & 0x02) printf("  - At least one slave in PREOP\n");
        if (ms.al_states & 0x04) printf("  - At least one slave in SAFEOP\n");
        if (ms.al_states & 0x08) printf("  - At least one slave in OP\n");
    }
    if (ms.link_up != master_state.link_up)
    {
        printf("EtherCAT link is %s\n", ms.link_up ? "UP" : "DOWN");
        if (!ms.link_up)
        {
            printf("  WARNING: EtherCAT link is down! Check network connection.\n");
        }
    }
    master_state = ms;
}

void EtherCATInterface::check_slave_config_states(ec_slave_config_t *sc, int i)
{
    ec_slave_config_state_t s;
    ecrt_slave_config_state(sc, &s);
    if (s.al_state != sc_state[i].al_state)
    {
        printf("Slave %d AL state: 0x%02X -> 0x%02X",
               i, sc_state[i].al_state, s.al_state);
        switch(s.al_state)
        {
            case 0x01:
                printf(" (INIT)\n");
                break;
            case 0x02:
                printf(" (PREOP)\n");
                break;
            case 0x04:
                printf(" (SAFEOP)\n");
                break;
            case 0x08:
                printf(" (OP)\n");
                break;
            default:
                printf(" (UNKNOWN/ERROR)\n");
                break;
        }
    }
    if (s.online != sc_state[i].online)
    {
        printf("Slave %d: %s\n", i, s.online ? "ONLINE" : "OFFLINE");
        if (!s.online)
        {
            printf("  WARNING: Slave %d went offline!\n", i);
        }
    }
    if (s.operational != sc_state[i].operational)
    {
        printf("Slave %d: %sOPERATIONAL\n", i, s.operational ? "" : "NOT ");
        if (!s.operational)
        {
            printf("  WARNING: Slave %d is not operational!\n", i);
        }
    }
    sc_state[i] = s;
}

bool EtherCATInterface::waitForSlavesOperational()
{
    const int max_attempts = 100;
    const int delay_ms = 50;

    printf("Waiting for slaves to reach operational state...\n");

    for (int attempt = 0; attempt < max_attempts; attempt++)
    {
        // Perform one communication cycle
        ecrt_master_receive(master_);
        ecrt_domain_process(domain_);

        // Check all slaves
        bool all_operational = true;
        for (int i = 0; i < NUM_SLAVES; i++)
        {
            ec_slave_config_state_t s;
            ecrt_slave_config_state(slave_config[i], &s);

            if (!s.operational)
            {
                all_operational = false;
                if (attempt % 20 == 0) // Print every second
                {
                    printf("Slave %d: %s, AL state: 0x%02X\n",
                           i, s.online ? "online" : "offline", s.al_state);
                }
            }
        }

        ecrt_domain_queue(domain_);
        ecrt_master_send(master_);

        if (all_operational)
        {
            printf("All slaves are operational!\n");
            return true;
        }

        usleep(delay_ms * 1000);
    }

    printf("Timeout waiting for slaves to become operational\n");
    return false;
}

bool EtherCATInterface::waitForAllSlavesInState(cia402_state_t target_state, int timeout_ms)
{
    const int delay_ms = 10;
    int attempts = timeout_ms / delay_ms;

    printf("Waiting for all slaves to reach state %d...\n", target_state);

    for (int attempt = 0; attempt < attempts; attempt++)
    {
        // Perform communication cycle
        ecrt_master_receive(master_);
        ecrt_domain_process(domain_);

        bool all_in_state = true;
        for (int i = 0; i < NUM_SLAVES; i++)
        {
            uint16_t status_word = EC_READ_U16(domain_pd_ + offset.Status_Word[i]);
            cia402_state_t current_state = get_axis_state(status_word);

            if (current_state != target_state)
            {
                all_in_state = false;
                if (attempt % 100 == 0) // Print every second
                {
                    printf("Slave %d: current state %d, target state %d\n",
                           i, current_state, target_state);
                }
            }
        }

        ecrt_domain_queue(domain_);
        ecrt_master_send(master_);

        if (all_in_state)
        {
            printf("All slaves reached target state %d\n", target_state);
            return true;
        }

        usleep(delay_ms * 1000);
    }

    printf("Timeout waiting for slaves to reach state %d\n", target_state);
    return false;
}

bool EtherCATInterface::verifySynchronization()
{
    static int sync_error_count = 0;
    bool sync_ok = true;

    // Check domain working counter
    ec_domain_state_t ds;
    ecrt_domain_state(domain_, &ds);

    if (ds.wc_state != EC_WC_COMPLETE)
    {
        sync_error_count++;
        sync_ok = false;
        printf("Domain sync error: WC state = %d\n", ds.wc_state);
    }

    // Check master state
    ec_master_state_t ms;
    ecrt_master_state(master_, &ms);

    if (ms.slaves_responding != NUM_SLAVES)
    {
        sync_error_count++;
        sync_ok = false;
        printf("Master sync error: Only %d/%d slaves responding\n",
               ms.slaves_responding, NUM_SLAVES);
    }

    if (!ms.link_up)
    {
        sync_error_count++;
        sync_ok = false;
        printf("Master sync error: Link is down\n");
    }

    // Check individual slave states
    for (int i = 0; i < NUM_SLAVES; i++)
    {
        ec_slave_config_state_t s;
        ecrt_slave_config_state(slave_config[i], &s);

        if (!s.online || !s.operational)
        {
            sync_error_count++;
            sync_ok = false;
            printf("Slave %d sync error: online=%d, operational=%d\n",
                   i, s.online, s.operational);
        }
    }

    // Check DC synchronization if enabled
#ifdef SYNC_MASTER_TO_REF
    if (abs(dc_diff_ns) > CYCLE_TIME_NS / 2) // More than half cycle drift
    {
        sync_error_count++;
        sync_ok = false;
        printf("DC sync error: drift = %d ns (> %d ns threshold)\n",
               dc_diff_ns, CYCLE_TIME_NS / 2);
    }
#endif

    if (sync_ok)
    {
        sync_error_count = 0; // Reset error count on success
    }
    else if (sync_error_count > 10) // Too many consecutive errors
    {
        printf("CRITICAL: %d consecutive sync errors detected!\n", sync_error_count);
        printSynchronizationStatus();
    }

    return sync_ok;
}

bool EtherCATInterface::performSynchronizationCheck()
{
    static uint32_t last_working_counter = 0;
    static int stuck_counter = 0;

    ec_domain_state_t ds;
    ecrt_domain_state(domain_, &ds);

    // Check if working counter is advancing
    if (ds.working_counter == last_working_counter)
    {
        stuck_counter++;
        if (stuck_counter > 50) // 5 seconds at 100ms intervals
        {
            printf("WARNING: Working counter stuck at %u for >5s\n", ds.working_counter);
            stuck_counter = 0;
            return false;
        }
    }
    else
    {
        stuck_counter = 0;
        last_working_counter = ds.working_counter;
    }

    return true;
}

void EtherCATInterface::printSynchronizationStatus()
{
    printf("\n=== EtherCAT Synchronization Status ===\n");

    // Domain status
    ec_domain_state_t ds;
    ecrt_domain_state(domain_, &ds);
    printf("Domain: WC=%u, WC_state=%d\n", ds.working_counter, ds.wc_state);

    // Master status
    ec_master_state_t ms;
    ecrt_master_state(master_, &ms);
    printf("Master: Slaves_responding=%u/%d, AL_states=0x%02X, Link=%s\n",
           ms.slaves_responding, NUM_SLAVES, ms.al_states,
           ms.link_up ? "UP" : "DOWN");

    // Individual slave status
    for (int i = 0; i < NUM_SLAVES; i++)
    {
        ec_slave_config_state_t s;
        ecrt_slave_config_state(slave_config[i], &s);

        uint16_t status_word = EC_READ_U16(domain_pd_ + offset.Status_Word[i]);
        cia402_state_t motor_state = get_axis_state(status_word);

        printf("Slave %d: AL=0x%02X, Online=%d, Op=%d, Motor_state=%d\n",
               i, s.al_state, s.online, s.operational, motor_state);
    }

#ifdef SYNC_MASTER_TO_REF
    printf("DC: diff=%d ns, started=%d\n", dc_diff_ns, dc_started);
#endif

    printf("=====================================\n\n");
}

cia402_state_t EtherCATInterface::get_axis_state(uint16_t status_word)
{
    if ((status_word & 0x4F) == 0x40)
        return switch_on_disable; // cannot enable
    if ((status_word & 0x6F) == 0x21)
        return ready_to_switch_on; // can enable
    if ((status_word & 0x6F) == 0x23)
        return switched_on; // can enable
    if ((status_word & 0x6F) == 0x27)
        return operation_enable; // can send position
    if ((status_word & 0x6F) == 0x07)
        return quick_stop_active;
    if ((status_word & 0x4F) == 0xF)
        return fault_reaction_active;
    if ((status_word & 0x4F) == 0x08)
        return fault;
    else
        return no_ready_to_switch_on;
}
